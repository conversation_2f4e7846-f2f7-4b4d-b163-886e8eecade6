<?php $__env->startSection('title', __('admin.dashboard')); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid pb-6">
    <div class="py-5">
        <div class="row g-4 align-items-center">
            <div class="col">
                <h1 class="h3 m-0"><?php echo e(__('admin.dashboard')); ?></h1>
                <p class="text-muted"><?php echo e(__('admin.welcome_back')); ?>, <?php echo e(Auth::user()->name); ?>!</p>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <i class="fas fa-newspaper fa-2x text-primary mb-2"></i>
                    <h6><?php echo e(__('admin.total_posts')); ?></h6>
                    <h4 id="totalPosts">0</h4>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <i class="fas fa-file-alt fa-2x text-info mb-2"></i>
                    <h6><?php echo e(__('admin.total_pages')); ?></h6>
                    <h4 id="totalPages">0</h4>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <i class="fas fa-users fa-2x text-success mb-2"></i>
                    <h6><?php echo e(__('admin.total_users')); ?></h6>
                    <h4 id="totalUsers">0</h4>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <i class="fas fa-comments fa-2x text-warning mb-2"></i>
                    <h6><?php echo e(__('admin.total_comments')); ?></h6>
                    <h4 id="totalComments">0</h4>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Posts -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0"><?php echo e(__('admin.recent_posts')); ?></h6>
                    <a href="<?php echo e(route('admin.posts.index')); ?>" class="btn btn-sm btn-outline-primary">
                        <?php echo e(__('admin.view_all')); ?>

                    </a>
                </div>
                <div class="card-body">
                    <div id="recentPosts">
                        <p class="text-muted"><?php echo e(__('admin.loading')); ?>...</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Comments -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0"><?php echo e(__('admin.recent_comments')); ?></h6>
                    <a href="<?php echo e(route('admin.comments.index')); ?>" class="btn btn-sm btn-outline-primary">
                        <?php echo e(__('admin.view_all')); ?>

                    </a>
                </div>
                <div class="card-body">
                    <div id="recentComments">
                        <p class="text-muted"><?php echo e(__('admin.loading')); ?>...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <!-- System Information -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><?php echo e(__('admin.system_information')); ?></h6>
                </div>
                <div class="card-body">
                    <table class="table table-sm">
                        <tr>
                            <td><?php echo e(__('admin.php_version')); ?>:</td>
                            <td><?php echo e(PHP_VERSION); ?></td>
                        </tr>
                        <tr>
                            <td><?php echo e(__('admin.laravel_version')); ?>:</td>
                            <td><?php echo e(app()->version()); ?></td>
                        </tr>
                        <tr>
                            <td><?php echo e(__('admin.environment')); ?>:</td>
                            <td>
                                <span class="badge bg-<?php echo e(app()->environment() === 'production' ? 'success' : 'warning'); ?>">
                                    <?php echo e(app()->environment()); ?>

                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td><?php echo e(__('admin.timezone')); ?>:</td>
                            <td><?php echo e(config('app.timezone')); ?></td>
                        </tr>
                        <tr>
                            <td><?php echo e(__('admin.locale')); ?>:</td>
                            <td><?php echo e(app()->getLocale()); ?></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><?php echo e(__('admin.quick_actions')); ?></h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="<?php echo e(route('admin.posts.index')); ?>" class="btn btn-outline-primary">
                            <i class="fas fa-plus me-2"></i><?php echo e(__('admin.add_new_post')); ?>

                        </a>
                        <a href="<?php echo e(route('admin.pages.index')); ?>" class="btn btn-outline-info">
                            <i class="fas fa-plus me-2"></i><?php echo e(__('admin.add_new_page')); ?>

                        </a>
                        <a href="<?php echo e(route('admin.users.index')); ?>" class="btn btn-outline-success">
                            <i class="fas fa-user-plus me-2"></i><?php echo e(__('admin.add_new_user')); ?>

                        </a>
                        <a href="<?php echo e(route('admin.settings.general')); ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-cog me-2"></i><?php echo e(__('admin.site_settings')); ?>

                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
$(document).ready(function() {
    loadDashboardStats();
    loadRecentPosts();
    loadRecentComments();
});

function loadDashboardStats() {
    // TODO: Load dashboard statistics
    $('#totalPosts').text('0');
    $('#totalPages').text('0');
    $('#totalUsers').text('0');
    $('#totalComments').text('0');
}

function loadRecentPosts() {
    // TODO: Load recent posts
    $('#recentPosts').html('<p class="text-muted"><?php echo e(__("admin.no_recent_posts")); ?></p>');
}

function loadRecentComments() {
    // TODO: Load recent comments
    $('#recentComments').html('<p class="text-muted"><?php echo e(__("admin.no_recent_comments")); ?></p>');
}
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH G:\Laragon\www\vietcms\resources\views/admin/dashboard.blade.php ENDPATH**/ ?>
<?php $__env->startSection('title', __('admin.posts')); ?>

<?php $__env->startSection('breadcrumbs'); ?>
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>"><?php echo e(__('admin.dashboard')); ?></a></li>
            <li class="breadcrumb-item active" aria-current="page"><?php echo e(__('admin.posts')); ?></li>
        </ol>
    </nav>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid pb-6">
    <div class="py-5">
        <div class="row g-4 align-items-center">
            <div class="col"><h1 class="h3 m-0"><?php echo e(__('admin.posts')); ?></h1></div>
            <div class="col-auto d-flex">
                <button type="button" class="btn btn-primary" onclick="createOrEditModal()">
                    <i class="fas fa-plus me-2"></i><?php echo e(__('admin.add_post')); ?>

                </button>
            </div>
        </div>
    </div>
    
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table id="postsTable" class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th><?php echo e(__('admin.title')); ?></th>
                            <th><?php echo e(__('admin.author')); ?></th>
                            <th><?php echo e(__('admin.status')); ?></th>
                            <th><?php echo e(__('admin.created_at')); ?></th>
                            <th><?php echo e(__('admin.actions')); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Data will be loaded via AJAX -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Modals will be loaded here -->
<div id="modalContainer"></div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
$(document).ready(function() {
    // Initialize DataTable
    var table = $('#postsTable').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '<?php echo e(route("admin.posts.getAll")); ?>',
            type: 'GET',
            data: function(d) {
                return {
                    skipCount: d.start,
                    maxResultCount: d.length,
                    sorting: d.columns[d.order[0].column].data + ' ' + d.order[0].dir
                };
            }
        },
        columns: [
            { data: 'title', name: 'title' },
            { data: 'author', name: 'author' },
            { data: 'status', name: 'status' },
            { data: 'created_at', name: 'created_at' },
            { data: 'actions', name: 'actions', orderable: false, searchable: false }
        ],
        order: [[3, 'desc']],
        pageLength: 10,
        responsive: true
    });
});

function createOrEditModal(id = null) {
    $.post('<?php echo e(route("admin.posts.modal")); ?>', {
        _token: '<?php echo e(csrf_token()); ?>',
        id: id
    }, function(data) {
        $('#modalContainer').html(data);
        $('#createOrEditModal').modal('show');
    });
}

function viewModal(id) {
    $.post('<?php echo e(route("admin.posts.view")); ?>', {
        _token: '<?php echo e(csrf_token()); ?>',
        id: id
    }, function(data) {
        $('#modalContainer').html(data);
        $('#viewModal').modal('show');
    });
}

function deletePost(id) {
    if (confirm('<?php echo e(__("admin.confirm_delete")); ?>')) {
        $.post('<?php echo e(route("admin.posts.destroy")); ?>', {
            _token: '<?php echo e(csrf_token()); ?>',
            id: id
        }, function(response) {
            if (response.success) {
                $('#postsTable').DataTable().ajax.reload();
                toastr.success(response.message);
            } else {
                toastr.error(response.message);
            }
        });
    }
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH G:\Laragon\www\vietcms\resources\views/admin/posts/index.blade.php ENDPATH**/ ?>